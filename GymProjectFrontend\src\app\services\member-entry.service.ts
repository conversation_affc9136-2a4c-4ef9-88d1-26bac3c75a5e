// src/app/services/member-entry.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { MemberEntry } from '../models/memberEntry';
import { BaseApiService } from './baseApiService';
import { PaginatedResult } from '../models/pagination';
import { MemberEntryPagingParameters } from '../models/memberEntryPagingParameters';
import { DataResponseModel } from '../models/dataResponseModel';

@Injectable({
  providedIn: 'root',
})
export class MemberEntryService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }

  getTodayEntries(date: string): Observable<ListResponseModel<MemberEntry>> {
    // Ensure date is properly formatted for backend
    console.log('Service sending date to API:', date);
    return this.httpClient.get<ListResponseModel<MemberEntry>>(
      `${this.apiUrl}member/gettodayentries?date=${encodeURIComponent(date)}`
    );
  }
getMemberEntriesBySearch(searchText: string): Observable<ListResponseModel<MemberEntry>> {
  return this.httpClient.get<ListResponseModel<MemberEntry>>(
      `${this.apiUrl}member/getmemberentriesbysearch?searchText=${searchText}`
  );
}

getTodayEntriesPaginated(parameters: MemberEntryPagingParameters): Observable<DataResponseModel<PaginatedResult<MemberEntry>>> {
  const params = new URLSearchParams();
  params.append('pageNumber', parameters.pageNumber.toString());
  params.append('pageSize', parameters.pageSize.toString());

  if (parameters.date) {
    params.append('date', parameters.date);
  }

  if (parameters.searchText) {
    params.append('searchText', parameters.searchText);
  }

  console.log('Service sending paginated request with params:', params.toString());
  return this.httpClient.get<DataResponseModel<PaginatedResult<MemberEntry>>>(
    `${this.apiUrl}member/gettodayentriespaginated?${params.toString()}`
  );
}
}
